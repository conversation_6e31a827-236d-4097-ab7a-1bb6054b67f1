-- Add more realistic schedule data for testing

-- Insert additional schedules for today and tomorrow
INSERT INTO schedules (client_name, client_id, client_email, client_phone, service_name, caregiver_id, start_time, end_time, location_id, status, notes, created_at, updated_at) VALUES
-- Today's schedules
('<PERSON><PERSON>', 105, '<EMAIL>', '+44 1232 212 3233', 'Service Name A', 1, datetime('now', '+1 hour'), datetime('now', '+2 hours'), 1, 'scheduled', 'Morning medication assistance', datetime('now'), datetime('now')),
('<PERSON><PERSON>', 105, '<EMAIL>', '+44 1232 212 3233', 'Service Name A', 1, datetime('now', '+3 hours'), datetime('now', '+4 hours'), 2, 'in_progress', 'Currently in progress', datetime('now'), datetime('now')),
('<PERSON>', 106, '<EMAIL>', '+44 1232 212 3237', 'Personal Care Service', 1, datetime('now', '+5 hours'), datetime('now', '+6 hours'), 3, 'scheduled', 'Evening care visit', datetime('now'), datetime('now')),
('<PERSON>', 107, '<EMAIL>', '+44 1232 212 3238', 'Medication Management', 1, datetime('now', '-2 hours'), datetime('now', '-1 hour'), 1, 'completed', 'Visit completed successfully', datetime('now'), datetime('now')),
('Michael <PERSON>', 108, '<EMAIL>', '+44 1232 212 3239', 'Companionship Service', 1, datetime('now', '-4 hours'), datetime('now', '-3 hours'), 2, 'completed', 'Medication administered', datetime('now'), datetime('now')),
('Linda Martinez', 109, '<EMAIL>', '+44 1232 212 3240', 'Personal Care Service', 1, datetime('now', '-6 hours'), datetime('now', '-5 hours'), 3, 'missed', 'Client was not available', datetime('now'), datetime('now')),

-- Tomorrow's schedules
('Melisa Adam', 105, '<EMAIL>', '+44 1232 212 3233', 'Service Name A', 1, datetime('now', '+1 day', '+2 hours'), datetime('now', '+1 day', '+3 hours'), 1, 'scheduled', 'Tomorrow morning visit', datetime('now'), datetime('now')),
('James Wilson', 106, '<EMAIL>', '+44 1232 212 3237', 'Personal Care Service', 1, datetime('now', '+1 day', '+4 hours'), datetime('now', '+1 day', '+5 hours'), 2, 'scheduled', 'Tomorrow afternoon visit', datetime('now'), datetime('now'));

-- Insert additional locations
INSERT OR IGNORE INTO locations (address, city, state, zip_code, latitude, longitude, created_at, updated_at) VALUES
('Casa Grande Apartment', 'Springfield', 'IL', '62704', 39.7817, -89.6501, datetime('now'), datetime('now')),
('Sunset Manor', 'Springfield', 'IL', '62705', 39.799, -89.644, datetime('now'), datetime('now')),
('Green Valley Residence', 'Springfield', 'IL', '62706', 39.7665, -89.6808, datetime('now'), datetime('now'));

-- Update existing schedules to use Casa Grande Apartment for Melisa Adam
UPDATE schedules SET location_id = 4 WHERE client_name = 'Melisa Adam' AND location_id != 4;
